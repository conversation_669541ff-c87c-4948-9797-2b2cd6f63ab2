<?php
/**
 * 测试 WorkbenchStatisticsService 模型查询转换
 * 这个文件用于验证从 Db::name 转换为模型查询是否正确
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\dashboard\service\WorkbenchStatisticsService;

// 模拟测试用户ID
$testUserId = 1;

try {
    $service = WorkbenchStatisticsService::getInstance();
    
    echo "=== 测试客户统计数据 ===\n";
    $customerStats = $service->getCustomerStatistics($testUserId);
    print_r($customerStats);
    
    echo "\n=== 测试合同统计数据 ===\n";
    $contractStats = $service->getContractStatistics($testUserId);
    print_r($contractStats);
    
    echo "\n=== 测试项目统计数据 ===\n";
    $projectStats = $service->getProjectStatistics($testUserId);
    print_r($projectStats);
    
    echo "\n=== 测试工作汇报摘要 ===\n";
    $workReports = $service->getWorkReportsSummary($testUserId, 3);
    print_r($workReports);
    
    echo "\n=== 测试企业新闻 ===\n";
    $companyNews = $service->getCompanyNews(3);
    print_r($companyNews);
    
    echo "\n✅ 所有测试通过！模型查询转换成功。\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
