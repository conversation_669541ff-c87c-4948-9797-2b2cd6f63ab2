<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api/system', function () {
	
	$nameSpace = 'app\system\controller';
	
	Route::post('upload', $nameSpace . '\AttachmentController@upload');
	Route::get('getUploadToken', $nameSpace . '\AttachmentController@getUploadToken');
	Route::get('getUploadConfig', $nameSpace . '\AttachmentController@getUploadConfig');
	Route::get('uploadCallback/:storage', $nameSpace . '\AttachmentController@uploadCallback');
	
	Route::post('logout', $nameSpace . '\AuthController@logout');
	
	Route::get('admin/info', $nameSpace . '\permission\AdminController@info');
	Route::get('admin/options', $nameSpace . '\permission\AdminController@options');
	
	
	Route::get('admin/permissions', $nameSpace . '\permission\AdminController@permissions');
	
	Route::post('admin/change_password', $nameSpace . '\permission\AdminController@changePassword');
	
	Route::get('menu/options', $nameSpace . '\permission\MenuController@options');
	Route::get('role/options', $nameSpace . '\permission\RoleController@options');
	Route::get('department/options', $nameSpace . '\permission\DepartmentController@options');
	Route::get('post/options', $nameSpace . '\permission\PostController@options');
	Route::get('tenant/options', $nameSpace . '\tenant\TenantList@options');
	
	
	Route::get('api/workflow/form_field/:id', 'app\workflow\controller\TypeController@getFormField');
	
	Route::get('type/businessOptions', 'app\workflow\controller\TypeController@businessOptions');
	
	Route::get('type/options', 'app\workflow\controller\TypeController@options');
	
	Route::get('article_category/options', $nameSpace . '\ArticleCategoryController@options');
	
	// 每日报价相关路由已迁移到 route/Daily.php
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     OperationLogMiddleware::class
     ]);

