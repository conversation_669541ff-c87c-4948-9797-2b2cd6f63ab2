<?php
declare(strict_types=1);

namespace app\dashboard\service;

use app\system\model\SystemArticle;
use think\facade\Db;

/**
 * 工作台统计服务类
 */
class WorkbenchStatisticsService
{
	// 存储单例实例
	private static ?WorkbenchStatisticsService $instance = null;
	
	/**
	 * 获取单例实例
	 */
	public static function getInstance(): WorkbenchStatisticsService
	{
		if (self::$instance === null) {
			self::$instance = new self();
		}
		return self::$instance;
	}
	
	/**
	 * 私有构造函数防止外部实例化
	 */
	private function __construct()
	{
		// 初始化逻辑
	}
	
	/**
	 * 获取客户统计数据
	 *
	 * @param int $userId 用户ID
	 * @return array
	 */
	public function getCustomerStatistics(int $userId): array
	{
		try {
			// 获取客户总数
			$totalCount = Db::name('crm_customer')
			                ->where('owner_user_id', $userId)
			                ->where('deleted_at', null)
			                ->count();
			
			// 获取本月新增客户数
			$monthlyNew = Db::name('crm_customer')
			                ->where('owner_user_id', $userId)
			                ->where('deleted_at', null)
			                ->whereTime('created_at', 'month')
			                ->count();
			
			// 获取上月新增客户数（用于计算环比）
			$lastMonthNew = Db::name('crm_customer')
			                  ->where('owner_user_id', $userId)
			                  ->where('deleted_at', null)
			                  ->whereTime('created_at', 'last month')
			                  ->count();
			
			// 计算环比增长率
			$growth      = 0;
			$growthClass = 'neutral';
			if ($lastMonthNew > 0) {
				$growth      = round((($monthlyNew - $lastMonthNew) / $lastMonthNew) * 100, 1);
				$growthClass = $growth > 0
					? 'positive'
					: ($growth < 0
						? 'negative'
						: 'neutral');
			}
			elseif ($monthlyNew > 0) {
				$growth      = 100;
				$growthClass = 'positive';
			}
			
			return [
				'total'        => $totalCount,
				'monthly_new'  => $monthlyNew,
				'growth'       => $growth,
				'growth_class' => $growthClass,
				'growth_text'  => $growth > 0
					? "+{$growth}%"
					: "{$growth}%"
			];
		}
		catch (\Exception $e) {
			return [
				'total'        => 0,
				'monthly_new'  => 0,
				'growth'       => 0,
				'growth_class' => 'neutral',
				'growth_text'  => '0%'
			];
		}
	}
	
	/**
	 * 获取合同统计数据
	 *
	 * @param int $userId 用户ID
	 * @return array
	 */
	public function getContractStatistics(int $userId): array
	{
		try {
			// 获取合同总金额
			$totalAmount = Db::name('crm_contract')
			                 ->where('owner_user_id', $userId)
			                 ->where('deleted_at', null)
			                 ->sum('contract_amount')
				?: 0;
			
			// 获取本月签约金额
			$monthlyAmount = Db::name('crm_contract')
			                   ->where('owner_user_id', $userId)
			                   ->where('deleted_at', null)
			                   ->whereTime('created_at', 'month')
			                   ->sum('contract_amount')
				?: 0;
			
			// 获取已完成合同数量
			$completedCount = Db::name('crm_contract')
			                    ->where('owner_user_id', $userId)
			                    ->where('deleted_at', null)
			                    ->where('status', 4) // 假设4是已完成状态
			                    ->count();
			
			// 获取合同总数
			$totalCount = Db::name('crm_contract')
			                ->where('owner_user_id', $userId)
			                ->where('deleted_at', null)
			                ->count();
			
			// 计算完成率
			$completionRate = $totalCount > 0
				? round(($completedCount / $totalCount) * 100, 1)
				: 0;
			
			return [
				'total_amount'         => $totalAmount,
				'total_amount_text'    => $this->formatAmount($totalAmount),
				'monthly_amount'       => $monthlyAmount,
				'monthly_amount_text'  => $this->formatAmount($monthlyAmount),
				'completion_rate'      => $completionRate,
				'completion_rate_text' => "{$completionRate}%"
			];
		}
		catch (\Exception $e) {
			return [
				'total_amount'         => 0,
				'total_amount_text'    => '0万',
				'monthly_amount'       => 0,
				'monthly_amount_text'  => '0万',
				'completion_rate'      => 0,
				'completion_rate_text' => '0%'
			];
		}
	}
	
	/**
	 * 获取项目统计数据
	 *
	 * @param int $userId 用户ID
	 * @return array
	 */
	public function getProjectStatistics(int $userId): array
	{
		try {
			// 获取进行中的项目数量
			$inProgressCount = Db::name('project_project')
			                     ->where('owner_id', $userId)
			                     ->where('deleted_at', null)
			                     ->where('status', 2) // 假设2是进行中状态
			                     ->count();
			
			// 获取已完成项目数量
			$completedCount = Db::name('project_project')
			                    ->where('owner_id', $userId)
			                    ->where('deleted_at', null)
			                    ->where('status', 3) // 假设3是已完成状态
			                    ->count();
			
			// 获取项目总数
			$totalCount = Db::name('project_project')
			                ->where('owner_id', $userId)
			                ->where('deleted_at', null)
			                ->count();
			
			// 计算完成率
			$completionRate = $totalCount > 0
				? round(($completedCount / $totalCount) * 100, 1)
				: 0;
			
			return [
				'in_progress'          => $inProgressCount,
				'completed'            => $completedCount,
				'total'                => $totalCount,
				'completion_rate'      => $completionRate,
				'completion_rate_text' => "{$completionRate}%"
			];
		}
		catch (\Exception $e) {
			return [
				'in_progress'          => 0,
				'completed'            => 0,
				'total'                => 0,
				'completion_rate'      => 0,
				'completion_rate_text' => '0%'
			];
		}
	}
	
	/**
	 * 获取工作汇报摘要
	 *
	 * @param int $userId 用户ID
	 * @param int $limit  限制数量
	 * @return array
	 */
	public function getWorkReportsSummary(int $userId, int $limit = 5): array
	{
		try {
			$reports = Db::name('crm_work_report')
			             ->alias('r')
			             ->leftJoin('system_admin a', 'r.creator_id = a.id')
			             ->field('r.id, r.title, r.content, r.type, r.created_at, a.real_name as reporter_name')
			             ->where('r.deleted_at', null)
			             ->order('r.created_at', 'desc')
			             ->limit($limit)
			             ->select()
			             ->toArray();
			
			foreach ($reports as &$report) {
				$report['type_text']       = $this->getReportTypeText($report['type']);
				$report['summary']         = mb_substr(strip_tags($report['content']), 0, 50) . '...';
				$report['created_at_text'] = date('m-d H:i', strtotime($report['created_at']));
			}
			
			return $reports;
		}
		catch (\Exception $e) {
			return [];
		}
	}
	
	/**
	 * 获取企业新闻
	 *
	 * @param int $limit 限制数量
	 * @return array
	 */
	public function getCompanyNews(int $limit = 5): array
	{
		try {
			$where = [
				[
					'status',
					'=',
					1
				],
				[
					'is_top',
					'=',
					1
				]
			];
			return SystemArticle::field('id, title, summary, is_top, created_at')
			                    ->where($where)
			                    ->order('created_at desc')
			                    ->limit($limit)
			                    ->select()
			                    ->toArray();
		}
		catch (\Exception $e) {
			return [];
		}
	}
	
	/**
	 * 格式化金额显示
	 *
	 * @param float $amount 金额
	 * @return string
	 */
	private function formatAmount(float $amount): string
	{
		if ($amount >= 10000) {
			return round($amount / 10000, 1) . '万';
		}
		elseif ($amount >= 1000) {
			return round($amount / 1000, 1) . '千';
		}
		else {
			return (string)$amount;
		}
	}
	
	/**
	 * 获取汇报类型文本
	 *
	 * @param string $type 类型
	 * @return string
	 */
	private function getReportTypeText(string $type): string
	{
		$types = [
			'daily'     => '日报',
			'weekly'    => '周报',
			'monthly'   => '月报',
			'quarterly' => '季报',
			'yearly'    => '年报'
		];
		return $types[$type] ?? '其他';
	}
}
