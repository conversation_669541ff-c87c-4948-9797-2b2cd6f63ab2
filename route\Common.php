<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api', function () {
	
	Route::get('workflow/form_field/:id', 'app\workflow\controller\TypeController@getFormField');
	
	// 流程类型获取所有模块
	Route::get('workflow/type/moduleOptions', 'app\workflow\controller\TypeController@moduleOptions');
	
	// 我的申请获取模块
	Route::get('workflow/myapp/moduleTypes', 'app\workflow\controller\ApplicationController@ModuleTypes');
	
	Route::get('workflow/type/businessOptions', 'app\workflow\controller\TypeController@businessOptions');
	
	Route::get('workflow/type/options', 'app\workflow\controller\TypeController@options');
	
	// 消息相关接口
	Route::get('notice/unread-count', 'app\notice\controller\MessageController@unreadCount');
	
	//	Route::post('notice/message/send', 'app\notice\controller\MessageController@send');
	
	Route::get('notice/module/options', 'app\notice\controller\NoticeTemplateController@moduleOptions');
	
	
	Route::post('notice/read/:id', 'app\notice\controller\MessageController@read');
	Route::post('notice/batchRead', 'app\notice\controller\MessageController@batchRead');
	Route::post('notice/allRead', 'app\notice\controller\MessageController@allRead');
	
	Route::get('notice/message/list', 'app\notice\controller\MessageController@list');
	Route::get('notice/message/detail/:id', 'app\notice\controller\MessageController@detail');
	
	// crm相关接口
	Route::get('crm/crm_customer_my/options', 'app\crm\controller\CrmCustomerMyController@options');
	
	Route::get('crm/crm_business/options', 'app\crm\controller\CrmBusinessController@options');
	
	Route::get('crm/crm_lead/options', 'app\crm\controller\CrmLeadController@options');
	
	// 每日报价
	Route::get('daily/daily_price_order/get_supplier_list', 'app\daily\controller\DailyPriceOrderController@getSupplierList');
	Route::get('daily/daily_price_order/get_product_list', 'app\daily\controller\DailyPriceOrderController@getProductList');
	Route::get('daily/daily_price_order/get_yesterday_prices', 'app\daily\controller\DailyPriceOrderController@getYesterdayPrices');
	
	// 供应商
	Route::get('ims/ims_supplier/options', 'app\ims\controller\ImsSupplierController@options');
	
	
	Route::get('crm/crm_product/options', 'app\crm\controller\CrmProductController@options');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     OperationLogMiddleware::class
     ]);

