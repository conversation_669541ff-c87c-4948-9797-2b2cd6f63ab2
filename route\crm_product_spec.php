<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 产品规格表路由
/*Route::group('api/crm/crm_product_spec', function () {
	Route::get('index', 'app\crm\controller\CrmProductSpecController@index');
	Route::get('detail/:id', 'app\crm\controller\CrmProductSpecController@detail');
	Route::post('add', 'app\crm\controller\CrmProductSpecController@add');
	Route::post('edit/:id', 'app\crm\controller\CrmProductSpecController@edit');
	Route::post('delete/:id', 'app\crm\controller\CrmProductSpecController@delete');
	Route::post('batchDelete', 'app\crm\controller\CrmProductSpecController@batchDelete');
	Route::post('updateField', 'app\crm\controller\CrmProductSpecController@updateField');
	Route::post('status/:id', 'app\crm\controller\CrmProductSpecController@status');
	Route::post('import', 'app\crm\controller\CrmProductSpecController@import');
	Route::get('importTemplate', 'app\crm\controller\CrmProductSpecController@importTemplate');
	Route::get('downloadTemplate', 'app\crm\controller\CrmProductSpecController@downloadTemplate');
	Route::get('export', 'app\crm\controller\CrmProductSpecController@export');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);*/