<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

/**
 * 消息通知系统路由
 * 包含：管理端接口和用户端接口
 */

Route::group('api/notice', function () {
	
	$nameSpace = 'app\notice\controller';
	
	// 消息模板管理
	Route::get('template/index', $nameSpace . '\NoticeTemplateController@list');
	Route::get('template/detail/:id', $nameSpace . '\NoticeTemplateController@detail');
	Route::post('template/add', $nameSpace . '\NoticeTemplateController@save');
	Route::post('template/edit/:id', $nameSpace . '\NoticeTemplateController@update');
	Route::post('template/delete/:id', $nameSpace . '\NoticeTemplateController@delete');
	// 预览模板
	Route::get('template/preview', $nameSpace . '\NoticeTemplateController@preview');
	
	Route::post('template/status/:id', $nameSpace . '\NoticeTemplateController@status');
	// 提取模板变量
	Route::post('template/variables/extract', $nameSpace . '\NoticeTemplateController@extractVariables');
	// 获取模板变量配置
	Route::get('template/variables/config/:id', $nameSpace . '\NoticeTemplateController@getVariablesConfig');
	// 保存模板变量配置
	Route::post('template/variables/config/:id', $nameSpace . '\NoticeTemplateController@saveVariablesConfig');
	
	// 模板配置管理
	Route::get('templateConfig/index', $nameSpace . '\TemplateConfigController@index');
	Route::get('templateConfig/detail/:id', $nameSpace . '\TemplateConfigController@/detail');
	Route::post('templateConfig/edit/:id', $nameSpace . '\TemplateConfigController@/edit');
	Route::post('templateConfig/set-status', $nameSpace . '\TemplateConfigController@/setStatus');
	Route::post('templateConfig/set-channel-status', $nameSpace . '\TemplateConfigController@/setChannelStatus');
	Route::post('templateConfig/test-send/:id', $nameSpace . '\TemplateConfigController@/testSend');
	Route::post('templateConfig/clone', $nameSpace . '\TemplateConfigController@clone');
	
	// 用户端接口：消息管理 消息列表和详情
	
	// 消息删除操作
//	Route::post('message/delete/:id', $nameSpace . '\MessageController@delete');
	
	// 消息状态操作
	//	Route::get('unread-count', $nameSpace . '\MessageController@unreadCount');
	
//	Route::post('batchDelete', $nameSpace . 'MessageController/batchDelete');
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class,
     ]);



// 管理端接口：系统设置和统计 todo 暂时不需要
/*Route::group('api/notice/system', function () {
    // 消息统计接口 - 需要实现
    Route::get('stats', 'SystemController/getMessageStats');
    Route::get('stats/channels', 'SystemController/getChannelStats');
    Route::get('stats/users', 'SystemController/getUserStats');
});*/

// 系统内部接口：消息队列处理 todo 暂时不需要
/*Route::group('api/notice-queue', function () {
    // 队列管理接口
    Route::get('list', 'QueueController/index');
    Route::get('detail/:id', 'QueueController/detail');
    
    Route::post('retry/:id', 'QueueController/retryQueueItem');
    
}); */